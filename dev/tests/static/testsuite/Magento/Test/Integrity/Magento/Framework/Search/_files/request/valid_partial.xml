<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<requests xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Search/etc/search_request.xsd">
    <request query="sugested_search_container_1" index="product_1">
        <queries>
            <query xsi:type="boolQuery" name="sugested_search_container" boost="2">
                <queryReference clause="must" ref="fulltext_search_query" />
                <queryReference clause="should" ref="suggested_search_container_dd" />
            </query>

            <query xsi:type="boolQuery" name="suggested_search_container_dd" boost="2">
                <queryReference clause="not" ref="fulltext_search_query" />
            </query>

            <query xsi:type="matchQuery" value="$fulltext_search_query$" name="fulltext_search_query" boost="5">
                <match field="title" />
                <match field="description" />
            </query>

            <query xsi:type="filteredQuery" name="promoted_documents_boost">
                <queryReference clause="must" ref="fulltext_search_query" />
            </query>

            <query xsi:type="filteredQuery" name="promoted_documents_boost2">
                <filterReference clause="must" ref="promoted_documents_filter" />
            </query>
        </queries>
        <filters>
            <filter xsi:type="termFilter" name="promoted_documents_filter" field="promoted" value="1" />
            <filter xsi:type="rangeFilter" field="promoted" name="price_name" from="10" to="100" />
            <filter xsi:type="boolFilter" name="price_name1">
                <filterReference clause="must" ref="promoted_boost" />
                <filterReference clause="must" ref="price" />
                <filterReference clause="must" ref="promoted_documents_filter" />
            </filter>
            <filter xsi:type="termFilter" name="promoted_boost" field="promoted" value="1" />
            <filter xsi:type="rangeFilter" field="promoted" name="price" from="10" to="100" />
        </filters>
        <aggregations>
            <bucket xsi:type="termBucket" name="category_bucket" field="category">
                <metrics>
                    <metric type="sum" />
                    <metric type="count" />
                    <metric type="min" />
                    <metric type="max" />
                </metrics>
            </bucket>
            <bucket xsi:type="rangeBucket" name="price_bucket" field="price">
                <metrics>
                    <metric type="sum" />
                    <metric type="count" />
                    <metric type="min" />
                    <metric type="max" />
                </metrics>
                <ranges>
                    <range from="" to="50" />
                    <range from="50" to="100" />
                    <range from="100" to="" />
                </ranges>
            </bucket>
            <bucket xsi:type="dynamicBucket" method="dynamic1" name="dynamic_price" field="price" />
        </aggregations>
        <from>10</from>
        <size>10</size>
    </request>
</requests>
