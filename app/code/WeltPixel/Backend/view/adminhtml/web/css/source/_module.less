@weltpixel_store_color: #FFF;

#nav {
    li.item-weltpixel {
        > a {
            &:before {
                content: '';
                background-image: url("../WeltPixel_Backend/images/WeltPixel_logo.svg");
                background-repeat: no-repeat;
                background-size: 70px;
                background-position: center;
                height: 50px;
            }
            > span {
                font-size: 0px;
            }
        }
        .submenu {
            .submenu-title {
                background-image: url("../WeltPixel_Backend/images/WeltPixel_logo.svg");
                background-repeat: no-repeat;
                padding-left: 80px;
                height: 80px;
                font-size: 0px;
                margin-bottom: 15px;
                background-size: 120px;
            }
            .item-weltpixel-documentation {
                a {
                    &:after {
                        content: '\e60d';
                        font-family: 'Admin Icons';
                        margin-left: 10px;
                    }
                }
            }
            .item-weltpixel-helpcenter {
                a {
                    &:after {
                        content: '\e633';
                        font-family: 'Admin Icons';
                        margin-left: 10px;
                        vertical-align: baseline;
                        font-size: 13px;
                    }
                }
            }
            .item-weltpixel-debugger {
                .submenu-group-title {
                    &:after {
                        content: '\e613';
                        font-family: 'Admin Icons';
                        margin-left: 10px;
                        color: #FFF;
                    }
                }
            }
            .item-pearl-theme-options {
                display: none;
                &.parent {
                    display: block
                }
            }
            .parent {
                margin-bottom: 1em;
                .submenu-group-title {
                    margin-bottom: 0px;
                    padding: 0.7rem 0.5rem;
                }
            }
            li {
                min-width: 20rem;
                &.level-1 {
                    margin-left: 0.5rem;
                    margin-right: 0.5rem;
                    a {
                        padding: 0.5rem 0.5rem;
                        font-size: 13px;
                    }
                }
            }
        }
    }
}

.config-nav {
    .item-weltpixel-documentation {
        a {
            &:after {
                content: '\e60d';
                font-family: 'Admin Icons';
                margin-left: 10px;
            }
        }
    }

    .weltpixel-tab {
        .admin__page-nav-title {
            strong {margin-left: 50px;}
            &:before {
                content: '';
                background-image: url("../WeltPixel_Backend/images/WeltPixel_logo_dark.svg");
                background-repeat: no-repeat;
                background-size: 40px;
                background-position: 0px 0px;
                height: 30px;
                position: absolute;
                width: 50px;
                top: 14px;
            }
        }
    }
}

#weltpixel_store {
    border: none;
    height: 1500px;
}

.section-config {
    &.weltpixel-recommended-header {
        .admin__collapsible-block {
            display: block !important;
            a {
                font-weight: normal;
                text-transform: uppercase;
                &:before {
                    content: '' !important;
                }
            }
            table {
                td.label {
                    display: none;
                }
                td.value {
                    width: 100%;
                }
            }
        }
    }
}

iframe#launcher {
    display: none !important;
}

.col-rewrite-status {width: 160px;}
.rewrite-status, .license-status {
    width: 100%;
    display: block;
    text-align: center;
    padding: 5px;
    font-weight: bold;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    color: #FFF;
}
.rewrite-status-nok, .license-status-nok {
    background-color: #ff0000;
}
.rewrite-status-ok, .license-status-ok {
    background-color: #00aa00;
}
.module-title {
    font-size: 16px;
}
.module-theme-info {
    font-size: 12px;
    font-style: italic;
    font-weight: bold;
}

.license-container {
    .admin__table-secondary {
        background-color: #FFF;
        tbody tr td {
            background-color: #FFF;
        }
    }
    .admin_table_filled {
        background-color: #f1f1f1;
        tbody tr td {
            background-color: #f1f1f1;
        }
    }
    th {
        &.license-name {
            width: 24%;
        }
        &.license-key {
            width: 68%;
        }
        &.license-validity {
            width: 8%;
        }
    }
    td {
        &.license-name {
            width: 24%;
            font-size: 16px;
        }
        &.license-key {
            width: 68%;
            word-break: break-all;
        }
        &.license-validity {
            width: 8%;
        }
    }
    .license-input {
        width: 85%;
        margin-right: 20px;
    }
}

.system-infromation-container {
    div {
        margin: 5px 0;
    }
    .information-label {
        text-align: right;
        font-weight: bold;
        padding-right: 2%;
        width: 47%;
    }
    .cronjob-header {
        padding: 10px;
        border: 1px solid;
        border-bottom: none;
        margin: 0;
        margin-top: 20px;
        table {
            th, td {
                text-align: right;
            }
            th {
                &.job-code {
                    width: 43%;
                }
                &.status {
                    width: 8%;
                }
                &.created-at {
                    width: 16%;
                }
                &.scheduled-at {
                    width: 16%;
                }
                &.executed-at {
                    width: 17%;
                }
            }
        }
    }
    .cronjob-details {
        margin-top: 0px;
        border: 1px solid;
        padding: 10px;
        height: 300px;
        overflow-y: scroll;
        table {
            th, td {
                text-align: right;
            }
            td {
                &.job-code {
                    width: 43%;
                }
                &.status {
                    width: 8%;
                }
                &.created-at {
                    width: 16%;
                }
                &.scheduled-at {
                    width: 16%;
                }
                &.executed-at {
                    width: 17%;
                }
            }
        }
    }
}

.license-pmsg {
    padding: 20px 10px;
    font-weight:700;
    color: #fff;
    letter-spacing:1px;
    &.invalid-license-pmsg {
        background: #f30d49;
    }
    &.valid-license-pmsg {
        background: #359639;
    }
}


.tfa-google-auth {
    .message-error {
        display: none;
    }
}

.section-config.weltpixel-module-information-header {
    .admin__collapsible-block {
        display: block !important;
        a {
            font-weight: normal;
            text-transform: initial;
            &:before {
                content: '' !important;
            }
        }
        table {
            td.label {
                display: none;
            }
            td.value {
                padding-top: 0;
                width: 100%;
            }
        }

        .wp-module-version {
            color: #1c8ecb;
            font-weight: 600;
        }

        .message {
            width: fit-content;
            p {
                margin: 0;
            }
        }

        .request-feature-button {
            padding: 20px 0 0 0;
            #request_feature {
                background-color: #073756;
                border: #fff;
                padding: 10px 15px;
                color: #FFF;
                font-weight: 600;
                &:hover {
                    background-color: #2f5e7d;
                    box-shadow: 0 0 10px #073756;
                }
            }
        }
    }

}
