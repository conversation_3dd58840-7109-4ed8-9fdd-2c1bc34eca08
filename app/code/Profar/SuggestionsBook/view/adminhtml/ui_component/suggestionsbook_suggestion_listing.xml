<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">suggestionsbook_suggestion_listing.suggestionsbook_suggestion_listing_data_source</item>
            <item name="deps" xsi:type="string">suggestionsbook_suggestion_listing.suggestionsbook_suggestion_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">suggestionsbook_suggestion_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">add</item>
                <item name="label" xsi:type="string" translate="true">Nuevo Reclamo</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/edit</item>
            </item>
        </item>
    </argument>
    <dataSource name="suggestionsbook_suggestion_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">suggestionsbook_suggestion_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">entity_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                </item>
            </argument>
        </argument>
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">entity_id</param>
            </storageConfig>
        </settings>
    </dataSource>
    <columns name="suggestionsbook_suggestion_columns">
        <column name="entity_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="created_at">
            <settings>
                <filter>dateRange</filter>
                <label translate="true">Fecha</label>
            </settings>
        </column>
        <column name="name">
            <settings>
                <filter>text</filter>
                <label translate="true">Nombre</label>
            </settings>
        </column>
        <column name="email">
            <settings>
                <filter>text</filter>
                <label translate="true">Email</label>
            </settings>
        </column>
        <column name="phone">
            <settings>
                <filter>text</filter>
                <label translate="true">Teléfono</label>
            </settings>
        </column>
        <column name="subject">
            <settings>
                <filter>text</filter>
                <label translate="true">Asunto</label>
            </settings>
        </column>
        <column name="description">
            <settings>
                <filter>text</filter>
                <label translate="true">Descripción</label>
            </settings>
        </column>
        <column name="status">
            <settings>
                <filter>select</filter>
                <options>
                    <option name="pending" xsi:type="array">
                        <item name="value" xsi:type="string">pending</item>
                        <item name="label" xsi:type="string" translate="true">Pendiente</item>
                    </option>
                    <option name="answered" xsi:type="array">
                        <item name="value" xsi:type="string">answered</item>
                        <item name="label" xsi:type="string" translate="true">Respondido</item>
                    </option>
                </options>
                <label translate="true">Estado</label>
            </settings>
        </column>
    </columns>
</listing> 