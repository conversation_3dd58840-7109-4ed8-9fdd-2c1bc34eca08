<?php

namespace Profar\CheckoutFields\Plugin\Checkout;

class LayoutProcessorPlugin
{
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        array $jsLayout
    ) {
        $customFields = [
            'document_type' => [
                'component' => 'Magento_Ui/js/form/element/select',
                'config' => [
                    'customScope' => 'shippingAddress',
                    'template' => 'ui/form/field',
                    'elementTmpl' => 'ui/form/element/select',
                    'id' => 'document_type',
                    'options' => [
                        ['value' => '', 'label' => __('Seleccione un tipo')],
                        ['value' => 'rut', 'label' => __('RUT')],
                    ]
                ],
                'dataScope' => 'shippingAddress.custom_attributes.document_type',
                'label' => __('Tipo de documento'),
                'provider' => 'checkoutProvider',
                'visible' => true,
                'validation' => ['required-entry' => true],
                'sortOrder' => 50
            ],
            'document_number' => [
                'component' => 'Magento_Ui/js/form/element/abstract',
                'config' => [
                    'customScope' => 'shippingAddress',
                    'template' => 'ui/form/field',
                    'elementTmpl' => 'ui/form/element/input',
                    'id' => 'document_number',
                    'dataType' => 'text',
                    'maxlength' => 11,
                    'formElement' => 'input'
                ],
                'dataScope' => 'shippingAddress.custom_attributes.document_number',
                'label' => __('Número de documento'),
                'provider' => 'checkoutProvider',
                'visible' => true,
                'validation' => [
                    'required-entry' => true,
                    'validate-document-length' => true
                ],
                'sortOrder' => 51
            ]
        ];

        foreach ($customFields as $fieldCode => $fieldConfig) {
            $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fieldset']['children'][$fieldCode] = $fieldConfig;
        }

        return $jsLayout;
    }
}
