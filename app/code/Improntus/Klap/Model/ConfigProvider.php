<?php

/**
 * Copyright © Improntus All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Improntus\Klap\Model;

use Improntus\Klap\Helper\Data as KlapHelper;
use Magento\Checkout\Model\ConfigProviderInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Asset\Repository as AssetRepository;

/**
 *
 */
class ConfigProvider implements ConfigProviderInterface
{
    public const CODE = 'klap';
    public const CODE_FLEX = 'klap_flex';
    public const BANNER = 'Improntus_Klap::images/logo.png';

    /**
     * @var AssetRepository
     *
     */
    private $assetRepository;
    /**
     * @var KlapHelper
     */
    private KlapHelper $klapHelper;

    /**
     * Constructor
     *
     * @param AssetRepository $assetRepository
     */
    public function __construct(
        AssetRepository $assetRepository,
        KlapHelper      $klapHelper
    ) {
        $this->assetRepository = $assetRepository;
        $this->klapHelper = $klapHelper;
    }

    /**
     * Retrieve assoc array of checkout configuration
     * @return array
     * @throws NoSuchEntityException
     */
    public function getConfig()
    {
        return [
            'payment' => [
                self::CODE => [
                    'active' => $this->klapHelper->isEnabled(self::CODE) && $this->klapHelper->validateCredentials(self::CODE),
                    'title' => $this->klapHelper->getTitle(self::CODE),
                    'logo' => $this->klapHelper->getLogo(self::CODE) ?: $this->assetRepository->getUrl(self::BANNER),
                    'code' => self::CODE,
                    'allowed_methods' => self::CODE,
                    'redirect_url' => $this->klapHelper->getRedirectUrl(),
                    'error_url' => $this->klapHelper->getFailUrl(),
                    'origin_url' => $this->klapHelper->getOriginUrl()
                ],
                self::CODE_FLEX => [
                    'active' => $this->klapHelper->isEnabled(self::CODE_FLEX) && $this->klapHelper->validateCredentials(self::CODE_FLEX),
                    'title' => $this->klapHelper->getTitle(self::CODE_FLEX),
                    'logo' => $this->klapHelper->getLogo(self::CODE_FLEX) ?: $this->assetRepository->getUrl(self::BANNER),
                    'code' => self::CODE_FLEX,
                    'allowed_methods' => self::CODE_FLEX,
                    'redirect_url' => $this->klapHelper->getRedirectUrl(),
                    'error_url' => $this->klapHelper->getFailUrl(),
                    'origin_url' => $this->klapHelper->getOriginUrl(),
                    'flex_script' => $this->klapHelper->getFlexScriptUrl(),
                    'is_sandbox' => $this->klapHelper->isSandbox(self::CODE_FLEX),
                ]
            ],
        ];
    }
}
