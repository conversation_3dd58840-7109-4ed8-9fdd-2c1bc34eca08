<?xml version="1.0" ?>
<!--
Copyright © Improntus All rights reserved.
See COPYING.txt for license details.
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\App\Request\CsrfValidator">
        <plugin name="csrf_validator_skip" type="Improntus\Klap\Plugin\CsrfValidatorSkip"/>
    </type>
    <preference for="Improntus\Klap\Api\KlapOrderInterface" type="Improntus\Klap\Model\KlapOrder"/>
    <preference for="Improntus\Klap\Api\TransactionRepositoryInterface"
                type="Improntus\Klap\Model\TransactionRepository"/>
    <preference for="Improntus\Klap\Api\Data\TransactionInterface" type="Improntus\Klap\Model\Transaction"/>
    <preference for="Improntus\Klap\Api\Data\TransactionSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults"/>
    <!-- @sensitive values -->
    <type name="Magento\Config\Model\Config\TypePool">
        <arguments>
            <argument name="sensitive" xsi:type="array">
                <item name="payment/klap/token" xsi:type="string">1</item>
                <item name="payment/klap/secret_key" xsi:type="string">1</item>
            </argument>
        </arguments>
    </type>

    <!-- @logger -->
    <type name="Improntus\Klap\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">klap</argument>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">Improntus\Klap\Logger\Handler\CustomHandler</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="KlapLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Improntus\Klap\Logger\Handler\CustomHandler</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- @virtual type for payment model -->
    <virtualType name="Klap" type="Magento\Payment\Model\Method\Adapter">
        <arguments>
            <argument name="code" xsi:type="const">Improntus\Klap\Model\ConfigProvider::CODE</argument>
            <argument name="formBlockType" xsi:type="string">Magento\Payment\Block\Form</argument>
            <argument name="infoBlockType" xsi:type="string">Magento\Payment\Block\Info</argument>
            <argument name="valueHandlerPool" xsi:type="object">KlapValueHandlerPool</argument>
            <argument name="validatorPool" xsi:type="object">KlapValidatorPool</argument>
        </arguments>
    </virtualType>
    <virtualType name="KlapFlex" type="Magento\Payment\Model\Method\Adapter">
        <arguments>
            <argument name="code" xsi:type="const">Improntus\Klap\Model\ConfigProvider::CODE_FLEX</argument>
            <argument name="formBlockType" xsi:type="string">Magento\Payment\Block\Form</argument>
            <argument name="infoBlockType" xsi:type="string">Magento\Payment\Block\Info</argument>
            <argument name="valueHandlerPool" xsi:type="object">KlapFlexValueHandlerPool</argument>
            <argument name="validatorPool" xsi:type="object">KlapValidatorPool</argument>
        </arguments>
    </virtualType>
    <virtualType name="KlapValueHandlerPool" type="Magento\Payment\Gateway\Config\ValueHandlerPool">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="default" xsi:type="string">KlapConfigValueHandler</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="KlapFlexValueHandlerPool" type="Magento\Payment\Gateway\Config\ValueHandlerPool">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="default" xsi:type="string">KlapFlexConfigValueHandler</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="KlapConfigValueHandler" type="Magento\Payment\Gateway\Config\ConfigValueHandler">
        <arguments>
            <argument name="configInterface" xsi:type="object">KlapConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="KlapFlexConfigValueHandler" type="Magento\Payment\Gateway\Config\ConfigValueHandler">
        <arguments>
            <argument name="configInterface" xsi:type="object">KlapFlexConfig</argument>
        </arguments>
    </virtualType>

    <virtualType name="KlapConfig" type="Magento\Payment\Gateway\Config\Config">
        <arguments>
            <argument name="methodCode" xsi:type="const">Improntus\Klap\Model\ConfigProvider::CODE</argument>
        </arguments>
    </virtualType>
    <virtualType name="KlapFlexConfig" type="Magento\Payment\Gateway\Config\Config">
        <arguments>
            <argument name="methodCode" xsi:type="const">Improntus\Klap\Model\ConfigProvider::CODE_FLEX</argument>
        </arguments>
    </virtualType>

    <virtualType name="KlapValidatorPool" type="Magento\Payment\Gateway\Validator\ValidatorPool">
        <arguments>
            <argument name="validators" xsi:type="array">
                <item name="currency" xsi:type="string">Improntus\Klap\Gateway\Validator\CurrencyValidator</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="Improntus\Klap\Model\ResourceModel\KlapLogs\Grid\Collection"
                 type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">klap_logs</argument>
            <argument name="resourceModel" xsi:type="string">Improntus\Klap\Model\ResourceModel\KlapLogs</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="klap_logs_grid_data_source" xsi:type="string">
                    Improntus\Klap\Model\ResourceModel\KlapLogs\Grid\Collection
                </item>
            </argument>
        </arguments>
    </type>
</config>
