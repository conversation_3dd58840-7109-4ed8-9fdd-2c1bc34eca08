<?php

namespace Improntus\Klap\Controller\Notification;

use I<PERSON><PERSON>tus\Klap\Helper\Data as KlapHelper;
use I<PERSON><PERSON>tus\Klap\Model\TransactionRepository;
use Improntus\Klap\Model\KlapLogsRepository;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Request\Http;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Webapi\Exception;
use Magento\Framework\Webapi\Rest\Response;
use Magento\Sales\Model\Order;

/**
 * Handles webhook_validation URL for availability
 */
class FailureNotification implements HttpPostActionInterface
{
    /**
     * @var Http
     */
    protected $request;

    /**
     * @var KlapHelper
     */
    protected $klapHelper;

    /**
     * @var Response
     */
    protected $response;

    /**
     * @var JsonFactory
     */
    private $jsonFactory;

    /**
     * @var TransactionRepository
     */
    private $transactionRepository;

    /**
     * @var Order
     */
    private $order;

    /**
     * @var KlapLogsRepository
     */
    private $klapLogsRepository;

    /**
     * @param Http $request
     * @param KlapHelper $klapHelper
     * @param Response $response
     * @param JsonFactory $jsonFactory
     * @param TransactionRepository $transactionRepository
     * @param Order $order
     * @param KlapLogsRepository $klapLogsRepository
     */
    public function __construct(
        Http                  $request,
        KlapHelper            $klapHelper,
        Response              $response,
        JsonFactory           $jsonFactory,
        TransactionRepository $transactionRepository,
        Order                 $order,
        KlapLogsRepository    $klapLogsRepository
    ) {
        $this->request = $request;
        $this->klapHelper = $klapHelper;
        $this->response = $response;
        $this->jsonFactory = $jsonFactory;
        $this->transactionRepository = $transactionRepository;
        $this->order = $order;
        $this->klapLogsRepository = $klapLogsRepository;
    }

    /**
     * Execute function
     *
     * @throws Exception
     */
    public function execute()
    {
        try {
            $data = json_decode($this->request->getContent());
            $this->klapLogsRepository->saveLogResponse($data);
            $transaction = null;
            if ($data->reference_id) {
                $transaction = $this->transactionRepository->getByQuoteId($data->reference_id);
            } else {
                throw new Exception(
                    __('There was an error processing the notification. Incorrect payload.'),
                    0,
                    Exception::HTTP_INTERNAL_ERROR
                );
            }
            $order = $this->order->loadByAttribute('quote_id', $data->reference_id);
            if(!$this->klapHelper->validateWebhook($data->reference_id,
                $data->order_id,
                $this->request->getHeader('apikey'))) {
                throw new Exception(
                    __(
                        'Webhook not authorized for this action.'
                    ),
                    0,
                    Exception::HTTP_INTERNAL_ERROR
                );
            }
            $this->transactionRepository->
            processNotificationTransaction($order, $transaction, $data, "CANCELED");
            $resultJson = $this->jsonFactory->create();
            $logNotification = [
                "klap_reference" => $data->order_id,
                "quote_id" => $data->reference_id,
                "order" => $order ? $order->getIncrementId() : '0',
                "klap_transaction_id" => $transaction ? $transaction->getTransactionId() : '0'
            ];
            $this->klapHelper->log(["message" => "Failure Notification Processed", "body" => $logNotification]);
            return $resultJson->setData($data);
        } catch (\Exception $exception) {
            $this->klapHelper->log(
                ["origin" => "Failure Notification",
                    "message" => $exception->getMessage(), "code" => $exception->getCode()]
            );
            throw new Exception(
                __('There was an error processing the notification.'),
                0,
                Exception::HTTP_INTERNAL_ERROR
            );
        }
    }
}
