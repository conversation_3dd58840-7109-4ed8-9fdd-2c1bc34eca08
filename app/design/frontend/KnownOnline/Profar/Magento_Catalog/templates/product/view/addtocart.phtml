<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Catalog\Block\Product\View $block */
$_product = $block->getProduct();
$special = '';

// Debug data for console
$debugData = [
    'product_id' => $_product->getId(),
    'product_sku' => $_product->getSku(),
    'condicion_de_venta_value' => null,
    'condicion_de_venta_text' => null,
    'special_features_value' => null,
    'special_features_text' => null,
    'final_special' => null
];

// Check condicion_de_venta attribute
$condicionAttribute = $_product->getCustomAttribute('condicion_de_venta');
if ($condicionAttribute) {
    $special = $condicionAttribute->getValue();
    $debugData['condicion_de_venta_value'] = $condicionAttribute->getValue();
    $debugData['condicion_de_venta_text'] = $special;
} else {
    $debugData['condicion_de_venta_value'] = 'NOT_FOUND';
}

// Also check special_features for comparison
$specialFeaturesAttribute = $_product->getCustomAttribute('special_features');
if ($specialFeaturesAttribute) {
    $specialFeaturesText = $_product->getAttributeText('special_features');
    $debugData['special_features_value'] = $specialFeaturesAttribute->getValue();
    $debugData['special_features_text'] = $specialFeaturesText;
} else {
    $debugData['special_features_value'] = 'NOT_FOUND';
}

$debugData['final_special'] = $special;
?>
<?php if ($special === 'Receta retenida'): ?>
    <div class="receta-retenida-container">
        <img class="image-receta" src="<?= $block->getViewFileUrl('images/logo-tienda-pdp-verdeNuevo.svg') ?>" alt="">
        <div>
            <span class="titulo-receta">Producto disponible solo compra presencial</span>
            <span class="contenido-receta">Este producto se encuentra disponible en local ubicado en Pérez Valenzuela 1077, Providencia, Santiago</span>
            <span class="link-receta"><a href="https://www.google.com/maps/place/P%C3%A9rez+Valenzuela+1077,+Providencia,+Regi%C3%B3n+Metropolitana,+Chile/data=!4m2!3m1!1s0x9662c5883f9a3205:0x63ed0a9edc64ad67?sa=X&ved=1t:242&ictx=111">Ver indicaciones de como llegar ></a></span>
        </div>
    </div>
<?php else: ?>
    <?php if ($_product->isSaleable()) : ?>
        <div class="box-tocart">
            <div class="fieldset">
                <?php if ($block->shouldRenderQuantity()) : ?>
                    <div class="field qty">
                        <label class="label" for="qty"><span><?= $block->escapeHtml(__('Qty')) ?></span></label>
                        <div class="qty-wrapper">
                            <button type="button" class="qty-btn-minus">-</button>
                            <input type="number"
                                name="qty"
                                id="qty"
                                min="0"
                                value="<?= $block->getProductDefaultQty() * 1 ?>"
                                title="<?= $block->escapeHtmlAttr(__('Qty')) ?>"
                                class="input-qty"
                                data-validate="<?= $block->escapeHtml(json_encode($block->getQuantityValidators())) ?>" />
                            <button type="button" class="qty-btn-plus">+</button>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="actions">
                    <button type="submit"
                        title="<?= $block->escapeHtmlAttr(__('Agregar a la Bolsa')) ?>"
                        class="action primary tocart"
                        id="product-addtocart-button">
                        <span><?= $block->escapeHtml(__('Agregar a la Bolsa')) ?></span>
                    </button>
                    <?= $block->getChildHtml('', true) ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script>
        require(['jquery'], function($) {
            $(document).ready(function() {
                // Debug console log
                console.log('=== DEBUG ADD TO CART ===');
                console.log(<?= json_encode($debugData) ?>);
                console.log('=== END DEBUG ===');
                $('.qty-btn-plus').off('click').on('click', function() {
                    let $input = $(this).closest('.qty-wrapper').find('.input-qty');
                    let val = parseInt($input.val(), 10) || 1;
                    $input.val(val + 1).trigger('change');
                });

                $('.qty-btn-minus').off('click').on('click', function() {
                    let $input = $(this).closest('.qty-wrapper').find('.input-qty');
                    let val = parseInt($input.val(), 10) || 1;
                    if (val > 1) {
                        $input.val(val - 1).trigger('change');
                    }
                });
            });
        });
    </script>
    <script type="text/x-magento-init">
        {
            "#product_addtocart_form": {
                "Magento_Catalog/js/validate-product": {}
            }
        }
    </script>
<?php endif; ?>